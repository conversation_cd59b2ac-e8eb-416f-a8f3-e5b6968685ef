import OnboardingOverlay, { OnboardingStep } from '@/components/OnboardingOverlay';
import { markOnboardingCompleted, ONBOARDING_PAGES } from '@/utils/storage';
import React, { useCallback, useRef, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { View } from 'react-native';

interface MainScreenOnboardingProps {
  visible: boolean;
  onComplete: () => void;
  onSkip: () => void;
  searchContainerRef?: React.RefObject<View>;
  difficultyContainerRef?: React.RefObject<View>;
  generateButtonRef?: React.RefObject<View>;
}

const MainScreenOnboarding: React.FC<MainScreenOnboardingProps> = ({
  visible,
  onComplete,
  onSkip,
  searchContainerRef,
  difficultyContainerRef,
  generateButtonRef,
}) => {
  const { t } = useTranslation();
  const [currentStepIndex, setCurrentStepIndex] = useState(0);
  const [elementPositions, setElementPositions] = useState<{
    search?: { x: number; y: number; width: number; height: number };
    difficulty?: { x: number; y: number; width: number; height: number };
    generateButton?: { x: number; y: number; width: number; height: number };
  }>({});

  // Measure element positions when onboarding becomes visible
  React.useEffect(() => {
    if (visible) {
      const measureElements = () => {
        const positions: typeof elementPositions = {};

        if (searchContainerRef?.current) {
          searchContainerRef.current.measure((x, y, width, height, pageX, pageY) => {
            positions.search = { x: pageX, y: pageY, width, height };
            setElementPositions(prev => ({ ...prev, search: positions.search }));
          });
        }

        if (difficultyContainerRef?.current) {
          difficultyContainerRef.current.measure((x, y, width, height, pageX, pageY) => {
            positions.difficulty = { x: pageX, y: pageY, width, height };
            setElementPositions(prev => ({ ...prev, difficulty: positions.difficulty }));
          });
        }

        if (generateButtonRef?.current) {
          generateButtonRef.current.measure((x, y, width, height, pageX, pageY) => {
            positions.generateButton = { x: pageX, y: pageY, width, height };
            setElementPositions(prev => ({ ...prev, generateButton: positions.generateButton }));
          });
        }
      };

      // Delay measurement to ensure elements are rendered
      setTimeout(measureElements, 100);
    }
  }, [visible, searchContainerRef, difficultyContainerRef, generateButtonRef]);

  const steps: OnboardingStep[] = [
    {
      id: 'welcome',
      title: t('onboarding.main.welcome.title'),
      description: t('onboarding.main.welcome.description'),
      position: 'center',
      showSkip: true,
    },
    {
      id: 'ingredient_search',
      title: t('onboarding.main.ingredientSearch.title'),
      description: t('onboarding.main.ingredientSearch.description'),
      targetElement: elementPositions.search,
      position: 'bottom',
    },
    {
      id: 'difficulty_selection',
      title: t('onboarding.main.difficultySelection.title'),
      description: t('onboarding.main.difficultySelection.description'),
      targetElement: elementPositions.difficulty,
      position: 'bottom',
    },
    {
      id: 'generate_recipe',
      title: t('onboarding.main.generateRecipe.title'),
      description: t('onboarding.main.generateRecipe.description'),
      targetElement: elementPositions.generateButton,
      position: 'top',
    },
    {
      id: 'complete',
      title: t('onboarding.main.complete.title'),
      description: t('onboarding.main.complete.description'),
      position: 'center',
      showSkip: false,
    },
  ];

  const handleNext = useCallback(() => {
    if (currentStepIndex < steps.length - 1) {
      setCurrentStepIndex(currentStepIndex + 1);
    }
  }, [currentStepIndex, steps.length]);

  const handlePrevious = useCallback(() => {
    if (currentStepIndex > 0) {
      setCurrentStepIndex(currentStepIndex - 1);
    }
  }, [currentStepIndex]);

  const handleComplete = useCallback(async () => {
    try {
      await markOnboardingCompleted(ONBOARDING_PAGES.MAIN_SCREEN);
      setCurrentStepIndex(0);
      onComplete();
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
      onComplete();
    }
  }, [onComplete]);

  const handleSkip = useCallback(async () => {
    try {
      await markOnboardingCompleted(ONBOARDING_PAGES.MAIN_SCREEN);
      setCurrentStepIndex(0);
      onSkip();
    } catch (error) {
      console.error('Error marking onboarding as completed:', error);
      onSkip();
    }
  }, [onSkip]);

  return (
    <OnboardingOverlay
      visible={visible}
      steps={steps}
      currentStepIndex={currentStepIndex}
      onNext={handleNext}
      onPrevious={handlePrevious}
      onSkip={handleSkip}
      onComplete={handleComplete}
    />
  );
};

export default MainScreenOnboarding;
